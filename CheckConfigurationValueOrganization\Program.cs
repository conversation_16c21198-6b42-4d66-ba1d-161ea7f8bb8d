using System;
using MySqlConnector;

namespace CheckConfigurationValueOrganization
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";
            
            try
            {
                Console.WriteLine("🔍 Checking configuration_value_organization table...");
                Console.WriteLine();
                
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");
                    Console.WriteLine();
                    
                    // Check if configuration_value_organization table exists
                    CheckTableExists(connection);
                    Console.WriteLine();
                    
                    // Check table schema
                    CheckTableSchema(connection);
                    Console.WriteLine();
                    
                    // Check if there are any ConnectWise entries
                    CheckConnectWiseEntries(connection);
                    Console.WriteLine();
                    
                    // Test the stored procedure directly
                    TestStoredProcedure(connection);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static void CheckTableExists(MySqlConnection connection)
        {
            Console.WriteLine("📋 Checking if configuration_value_organization table exists:");
            
            var sql = "SHOW TABLES LIKE 'configuration_value_organization';";
            
            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        Console.WriteLine("✅ configuration_value_organization table exists");
                    }
                    else
                    {
                        Console.WriteLine("❌ configuration_value_organization table does NOT exist");
                        Console.WriteLine("   This explains why the stored procedure returns no results!");
                    }
                }
            }
        }
        
        static void CheckTableSchema(MySqlConnection connection)
        {
            Console.WriteLine("📋 configuration_value_organization Table Schema:");
            
            try
            {
                var sql = "DESCRIBE configuration_value_organization;";
                
                using (var cmd = new MySqlCommand(sql, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (!reader.HasRows)
                        {
                            Console.WriteLine("❌ Table has no schema (doesn't exist)");
                            return;
                        }
                        
                        Console.WriteLine("Field\t\t\tType\t\t\tNull\tKey\tDefault\tExtra");
                        Console.WriteLine("-----\t\t\t----\t\t\t----\t---\t-------\t-----");
                        
                        while (reader.Read())
                        {
                            string field = reader.GetString(0);
                            string type = reader.GetString(1);
                            string nullValue = reader.GetString(2);
                            string key = reader.GetString(3);
                            string defaultValue = reader.IsDBNull(4) ? "NULL" : reader.GetString(4);
                            string extra = reader.GetString(5);
                            
                            Console.WriteLine($"{field}\t\t{type}\t\t{nullValue}\t{key}\t{defaultValue}\t{extra}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking schema: {ex.Message}");
            }
        }
        
        static void CheckConnectWiseEntries(MySqlConnection connection)
        {
            Console.WriteLine("📋 ConnectWise entries in configuration_value_organization:");
            
            try
            {
                var sql = @"
                    SELECT cvo.*, cv.Name, c.Category 
                    FROM configuration_value_organization cvo
                    INNER JOIN ConfigurationValues cv ON cvo.ConfigurationValuesId = cv.ConfigurationValuesId
                    INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId
                    WHERE c.Category = 'ConnectWise Api';";
                
                using (var cmd = new MySqlCommand(sql, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (!reader.HasRows)
                        {
                            Console.WriteLine("❌ No ConnectWise entries found in configuration_value_organization");
                            Console.WriteLine("   This is why GetConfigurationValuesPerOrganization returns empty results!");
                            return;
                        }
                        
                        Console.WriteLine("Found ConnectWise entries:");
                        while (reader.Read())
                        {
                            // Print all columns
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                Console.Write($"{reader.GetName(i)}: {reader.GetValue(i)}\t");
                            }
                            Console.WriteLine();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking entries: {ex.Message}");
            }
        }
        
        static void TestStoredProcedure(MySqlConnection connection)
        {
            Console.WriteLine("📋 Testing GetConfigurationValuesPerOrganization stored procedure:");
            
            try
            {
                var sql = "CALL GetConfigurationValuesPerOrganization(0, 'ConnectWise Api', NULL);";
                
                using (var cmd = new MySqlCommand(sql, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (!reader.HasRows)
                        {
                            Console.WriteLine("❌ Stored procedure returns NO RESULTS for ConnectWise Api");
                            Console.WriteLine("   This confirms why cwConfig.BaseUrl is null in ConnectWiseApi.cs");
                            return;
                        }
                        
                        Console.WriteLine("✅ Stored procedure returns results:");
                        Console.WriteLine("OrgId\tConfigId\tName\t\t\tValue\t\t\t\tIsSecret\tInputType\tCategory\tId");
                        Console.WriteLine("-----\t--------\t----\t\t\t-----\t\t\t\t--------\t---------\t--------\t--");
                        
                        while (reader.Read())
                        {
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                Console.Write($"{reader.GetValue(i)}\t");
                            }
                            Console.WriteLine();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error testing stored procedure: {ex.Message}");
            }
        }
    }
}
