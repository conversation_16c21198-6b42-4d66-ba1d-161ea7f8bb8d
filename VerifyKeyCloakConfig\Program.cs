using System;
using MySqlConnector;

namespace VerifyKeyCloakConfig
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

            try
            {
                Console.WriteLine("🔍 Verifying KeyCloak configuration in database...");
                Console.WriteLine();

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");

                    // Check KeyCloak configuration
                    var verifySql = @"
                        SELECT
                            c.Category,
                            cv.Name,
                            CASE
                                WHEN cv.IsSecret = 1 AND cv.Value != '' THEN '***HIDDEN***'
                                ELSE cv.Value
                            END as Value,
                            cv.IsSecret
                        FROM Configuration c
                        INNER JOIN ConfigurationValues cv ON c.ConfigurationId = cv.ConfigurationId
                        WHERE c.Category = 'KeyCloak'
                        ORDER BY cv.Name;";

                    using (var cmd = new MySqlCommand(verifySql, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (!reader.HasRows)
                            {
                                Console.WriteLine("❌ No KeyCloak configuration found in database.");
                                return;
                            }

                            Console.WriteLine("📋 Current KeyCloak Configuration:");
                            Console.WriteLine("Category\t\tName\t\t\tValue\t\t\t\t\tIsSecret");
                            Console.WriteLine("--------\t\t----\t\t\t-----\t\t\t\t\t--------");

                            while (reader.Read())
                            {
                                string category = reader.GetString("Category");
                                string name = reader.GetString("Name");
                                string value = reader.GetString("Value");
                                bool isSecret = reader.GetBoolean("IsSecret");

                                Console.WriteLine($"{category}\t\t{name}\t\t{value}\t\t{isSecret}");
                            }
                        }
                    }

                    Console.WriteLine("\n✅ KeyCloak configuration verification completed!");
                    Console.WriteLine("\n🚀 The backend should now be able to load KeyCloak configuration properly.");
                    Console.WriteLine("   This will fix the 401 authentication error and redirect to KeyCloak SSO.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Environment.Exit(1);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
