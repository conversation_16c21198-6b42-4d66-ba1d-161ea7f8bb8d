using System;
using System.Collections.Generic;
using MySqlConnector;

namespace FixConnectWiseConfiguration
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

            try
            {
                Console.WriteLine("🔧 Fixing ConnectWise API Configuration for Organization-Specific Access...");
                Console.WriteLine();

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");
                    Console.WriteLine();

                    // Step 1: Get all ConnectWise configuration values
                    var configValues = GetConnectWiseConfigurationValues(connection);

                    if (configValues.Count == 0)
                    {
                        Console.WriteLine("❌ No ConnectWise configuration values found in ConfigurationValues table");
                        return;
                    }

                    Console.WriteLine($"✅ Found {configValues.Count} ConnectWise configuration values");
                    Console.WriteLine();

                    // Step 2: Insert them into configuration_value_organization for root org (ID=0)
                    InsertOrganizationSpecificValues(connection, configValues);

                    Console.WriteLine();

                    // Step 3: Test the stored procedure
                    TestStoredProcedure(connection);

                    Console.WriteLine();
                    Console.WriteLine("✅ ConnectWise API configuration fix completed!");
                    Console.WriteLine("🎯 The ArgumentNullException in ConnectWiseApi.cs line 29 should now be resolved.");
                    Console.WriteLine();
                    Console.WriteLine("🚀 Next Steps:");
                    Console.WriteLine("1. Restart your backend application");
                    Console.WriteLine("2. The ConnectWise API should now initialize properly");
                    Console.WriteLine("3. Test any ConnectWise API functionality to verify the fix");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static List<(int ConfigurationValuesId, string Name, string Value)> GetConnectWiseConfigurationValues(MySqlConnection connection)
        {
            Console.WriteLine("📋 Getting ConnectWise configuration values from ConfigurationValues table:");

            var values = new List<(int, string, string)>();

            var sql = @"
                SELECT cv.ConfigurationValuesId, cv.Name, cv.Value
                FROM ConfigurationValues cv
                INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId
                WHERE c.Category = 'ConnectWise Api'
                ORDER BY cv.Name;";

            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        int configValuesId = reader.GetInt32("ConfigurationValuesId");
                        string name = reader.GetString("Name");
                        string value = reader.GetString("Value");

                        values.Add((configValuesId, name, value));

                        // Mask secret values for display
                        string displayValue = name.Contains("Private") || name.Contains("Secret") ? "***HIDDEN***" : value;
                        Console.WriteLine($"  - {name}: {displayValue}");
                    }
                }
            }

            return values;
        }

        static void InsertOrganizationSpecificValues(MySqlConnection connection, List<(int ConfigurationValuesId, string Name, string Value)> configValues)
        {
            Console.WriteLine("🔧 Inserting ConnectWise configuration into configuration_value_organization:");

            int insertedCount = 0;
            int skippedCount = 0;

            foreach (var (configValuesId, name, value) in configValues)
            {
                // Check if this value already exists for organization 0
                var checkSql = @"
                    SELECT COUNT(*) 
                    FROM configuration_value_organization 
                    WHERE ConfigurationValuesId = @configValuesId AND OrganizationId = 0;";

                using (var checkCmd = new MySqlCommand(checkSql, connection))
                {
                    checkCmd.Parameters.AddWithValue("@configValuesId", configValuesId);
                    int existingCount = Convert.ToInt32(checkCmd.ExecuteScalar());

                    if (existingCount > 0)
                    {
                        Console.WriteLine($"  ⏭️  {name}: Already exists, skipping");
                        skippedCount++;
                        continue;
                    }
                }

                // Insert the value for organization 0
                var insertSql = @"
                    INSERT INTO configuration_value_organization 
                    (ConfigurationValuesId, OrganizationId, Value, CreatedBy, CreatedOn)
                    VALUES (@configValuesId, 0, @value, 1, NOW());";

                using (var insertCmd = new MySqlCommand(insertSql, connection))
                {
                    insertCmd.Parameters.AddWithValue("@configValuesId", configValuesId);
                    insertCmd.Parameters.AddWithValue("@value", value);

                    int rowsAffected = insertCmd.ExecuteNonQuery();
                    if (rowsAffected > 0)
                    {
                        string displayValue = name.Contains("Private") || name.Contains("Secret") ? "***HIDDEN***" : value;
                        Console.WriteLine($"  ✅ {name}: Inserted successfully");
                        insertedCount++;
                    }
                    else
                    {
                        Console.WriteLine($"  ❌ {name}: Failed to insert");
                    }
                }
            }

            Console.WriteLine($"\n📊 Summary: {insertedCount} inserted, {skippedCount} skipped");
        }

        static void TestStoredProcedure(MySqlConnection connection)
        {
            Console.WriteLine("🧪 Testing GetConfigurationValuesPerOrganization stored procedure:");

            var sql = "CALL GetConfigurationValuesPerOrganization(0, 'ConnectWise Api', NULL);";

            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    if (!reader.HasRows)
                    {
                        Console.WriteLine("❌ Stored procedure still returns no results");
                        return;
                    }

                    Console.WriteLine("✅ Stored procedure now returns results:");

                    var baseUrlFound = false;
                    int resultCount = 0;

                    while (reader.Read())
                    {
                        resultCount++;
                        string name = reader.GetString("Name");
                        string value = reader.GetString("Value");

                        if (name == "CWBaseUrl")
                        {
                            baseUrlFound = true;
                            Console.WriteLine($"  🎯 {name}: {value} ✅");
                        }
                        else
                        {
                            string displayValue = name.Contains("Private") || name.Contains("Secret") ? "***HIDDEN***" : value;
                            Console.WriteLine($"  - {name}: {displayValue}");
                        }
                    }

                    Console.WriteLine($"\n📊 Total results: {resultCount}");

                    if (baseUrlFound)
                    {
                        Console.WriteLine("🎉 CWBaseUrl is now available - ArgumentNullException should be fixed!");
                    }
                    else
                    {
                        Console.WriteLine("❌ CWBaseUrl still not found in results");
                    }
                }
            }
        }
    }
}
