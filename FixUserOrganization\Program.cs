using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Fixing User_Organization table schema...");
    Console.WriteLine();

    // Check if User_Organization table exists
    var checkTableCmd = new MySqlCommand(@"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = 'myadaptivecloud' 
        AND TABLE_NAME = 'User_Organization'", connection);
    
    var tableExists = Convert.ToInt32(checkTableCmd.ExecuteScalar()) > 0;
    
    if (!tableExists)
    {
        Console.WriteLine("❌ User_Organization table does not exist!");
        Console.WriteLine("This table is required for user context retrieval.");
        Console.WriteLine();
        
        // Create the User_Organization table
        Console.WriteLine("Creating User_Organization table...");
        var createTableCmd = new MySqlCommand(@"
            CREATE TABLE User_Organization (
                Id INT AUTO_INCREMENT PRIMARY KEY,
                UserId INT NOT NULL,
                OrganizationId INT NOT NULL,
                is_contact BIT(1) DEFAULT 0 NOT NULL,
                INDEX idx_user_org (UserId, OrganizationId),
                INDEX idx_org_user (OrganizationId, UserId)
            )", connection);
        
        createTableCmd.ExecuteNonQuery();
        Console.WriteLine("✅ User_Organization table created!");
        
        // Insert a record for the ***** user
        Console.WriteLine("Adding ***** user to root organization...");
        var insertCmd = new MySqlCommand(@"
            INSERT INTO User_Organization (UserId, OrganizationId, is_contact) 
            VALUES (1, 0, 0)", connection);
        
        insertCmd.ExecuteNonQuery();
        Console.WriteLine("✅ Admin user added to User_Organization!");
    }
    else
    {
        Console.WriteLine("✓ User_Organization table exists.");
        
        // Check if is_contact column exists
        var checkColumnCmd = new MySqlCommand(@"
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'myadaptivecloud' 
            AND TABLE_NAME = 'User_Organization' 
            AND COLUMN_NAME = 'is_contact'", connection);
        
        var columnExists = Convert.ToInt32(checkColumnCmd.ExecuteScalar()) > 0;
        
        if (!columnExists)
        {
            Console.WriteLine("Adding missing 'is_contact' column...");
            var addColumnCmd = new MySqlCommand(@"
                ALTER TABLE User_Organization 
                ADD COLUMN is_contact BIT(1) DEFAULT 0 NOT NULL", connection);
            
            addColumnCmd.ExecuteNonQuery();
            Console.WriteLine("✅ Column 'is_contact' added successfully!");
        }
        else
        {
            Console.WriteLine("✓ Column 'is_contact' already exists.");
        }
        
        // Check if ***** user exists in User_Organization
        var checkUserCmd = new MySqlCommand(@"
            SELECT COUNT(*) 
            FROM User_Organization 
            WHERE UserId = 1 AND OrganizationId = 0", connection);
        
        var userExists = Convert.ToInt32(checkUserCmd.ExecuteScalar()) > 0;
        
        if (!userExists)
        {
            Console.WriteLine("Adding ***** user to User_Organization...");
            var insertCmd = new MySqlCommand(@"
                INSERT INTO User_Organization (UserId, OrganizationId, is_contact) 
                VALUES (1, 0, 0)", connection);
            
            insertCmd.ExecuteNonQuery();
            Console.WriteLine("✅ Admin user added to User_Organization!");
        }
        else
        {
            Console.WriteLine("✓ Admin user already exists in User_Organization.");
        }
    }

    Console.WriteLine();
    Console.WriteLine("✅ User_Organization table schema fix complete!");
    
    // Show the table structure
    Console.WriteLine("\nUser_Organization table structure:");
    var describeCmd = new MySqlCommand("DESCRIBE User_Organization", connection);
    using var reader = describeCmd.ExecuteReader();
    while (reader.Read())
    {
        Console.WriteLine($"  Column: {reader["Field"]}, Type: {reader["Type"]}, Default: {reader["Default"]}");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
