using System;
using System.Linq;
using System.Collections.Generic;
using MySqlConnector;

namespace CheckConnectWiseConfig
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

            try
            {
                Console.WriteLine("🔍 Checking ConnectWise API configuration in database...");
                Console.WriteLine();

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");

                    // Check ConnectWise configuration
                    var checkSql = @"
                        SELECT 
                            c.Category,
                            cv.Name,
                            CASE 
                                WHEN cv.IsSecret = 1 AND cv.Value != '' THEN '***HIDDEN***'
                                ELSE cv.Value 
                            END as Value,
                            cv.IsSecret
                        FROM Configuration c 
                        INNER JOIN ConfigurationValues cv ON c.ConfigurationId = cv.ConfigurationId 
                        WHERE c.Category = 'ConnectWise Api'
                        ORDER BY cv.Name;";

                    using (var cmd = new MySqlCommand(checkSql, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (!reader.HasRows)
                            {
                                Console.WriteLine("❌ No ConnectWise API configuration found in database.");
                                Console.WriteLine("   This explains the ArgumentNullException in ConnectWiseApi.cs line 29.");
                                Console.WriteLine();
                                ShowRequiredConfiguration();
                                return;
                            }

                            Console.WriteLine("📋 Current ConnectWise API Configuration:");
                            Console.WriteLine("Category\t\tName\t\t\tValue\t\t\t\t\tIsSecret");
                            Console.WriteLine("--------\t\t----\t\t\t-----\t\t\t\t\t--------");

                            var foundValues = new List<string>();
                            while (reader.Read())
                            {
                                string category = reader.GetString("Category");
                                string name = reader.GetString("Name");
                                string value = reader.GetString("Value");
                                bool isSecret = reader.GetBoolean("IsSecret");

                                foundValues.Add(name);
                                Console.WriteLine($"{category}\t{name}\t\t{value}\t\t{isSecret}");
                            }

                            Console.WriteLine();
                            CheckMissingValues(foundValues);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Environment.Exit(1);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static void ShowRequiredConfiguration()
        {
            Console.WriteLine("📋 Required ConnectWise API Configuration Values:");
            Console.WriteLine("- CWBaseUrl (ConnectWise API base URL)");
            Console.WriteLine("- CWClientId (ConnectWise Client ID)");
            Console.WriteLine("- CWCompany (ConnectWise Company identifier)");
            Console.WriteLine("- CWPublicKey (ConnectWise Public Key)");
            Console.WriteLine("- CWPrivateKey (ConnectWise Private Key)");
        }

        static void CheckMissingValues(List<string> foundValues)
        {
            var requiredValues = new[] { "CWBaseUrl", "CWClientId", "CWCompany", "CWPublicKey", "CWPrivateKey" };
            var missingValues = requiredValues.Where(rv => !foundValues.Contains(rv)).ToList();

            if (missingValues.Any())
            {
                Console.WriteLine("❌ Missing Required Configuration Values:");
                foreach (var missing in missingValues)
                {
                    Console.WriteLine($"   - {missing}");
                }
                Console.WriteLine();
                Console.WriteLine("💡 The missing values are causing the ArgumentNullException in ConnectWiseApi.cs");
            }
            else
            {
                Console.WriteLine("✅ All required ConnectWise configuration values are present.");
                Console.WriteLine("💡 The issue might be that one of the values is empty/null.");
            }
        }
    }
}
