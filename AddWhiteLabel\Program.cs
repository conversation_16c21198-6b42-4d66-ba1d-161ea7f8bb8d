using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Adding WhiteLabel configuration for localhost:5001...");
    Console.WriteLine();

    // Check if entry already exists
    var checkCmd = new MySqlCommand("SELECT COUNT(*) FROM WhiteLabel WHERE AdaptiveCloudHostname = 'localhost:5001'", connection);
    var count = Convert.ToInt32(checkCmd.ExecuteScalar());
    
    if (count > 0)
    {
        Console.WriteLine("✓ WhiteLabel entry for localhost:5001 already exists.");
    }
    else
    {
        // Insert new white label entry
        var insertCmd = new MySqlCommand(@"
            INSERT INTO WhiteLabel 
            (OrganizationId, PortalName, DomainName, PrimaryColor, SecondaryColor, CreatedDate, IsActive, AdaptiveCloudHostname) 
            VALUES 
            (0, 'MyAdaptiveCloud', 'localhost:5001', '#3079A7', '#666666', NOW(), 1, 'localhost:5001')", connection);
        
        int rowsAffected = insertCmd.ExecuteNonQuery();
        
        if (rowsAffected > 0)
        {
            Console.WriteLine("✓ WhiteLabel entry for localhost:5001 added successfully!");
        }
        else
        {
            Console.WriteLine("✗ Failed to add WhiteLabel entry.");
        }
    }

    // Also add entry for localhost:4200 (frontend)
    var checkFrontendCmd = new MySqlCommand("SELECT COUNT(*) FROM WhiteLabel WHERE AdaptiveCloudHostname = 'localhost:4200'", connection);
    var frontendCount = Convert.ToInt32(checkFrontendCmd.ExecuteScalar());
    
    if (frontendCount > 0)
    {
        Console.WriteLine("✓ WhiteLabel entry for localhost:4200 already exists.");
    }
    else
    {
        var insertFrontendCmd = new MySqlCommand(@"
            INSERT INTO WhiteLabel 
            (OrganizationId, PortalName, DomainName, PrimaryColor, SecondaryColor, CreatedDate, IsActive, AdaptiveCloudHostname) 
            VALUES 
            (0, 'MyAdaptiveCloud', 'localhost:4200', '#3079A7', '#666666', NOW(), 1, 'localhost:4200')", connection);
        
        int frontendRowsAffected = insertFrontendCmd.ExecuteNonQuery();
        
        if (frontendRowsAffected > 0)
        {
            Console.WriteLine("✓ WhiteLabel entry for localhost:4200 added successfully!");
        }
        else
        {
            Console.WriteLine("✗ Failed to add WhiteLabel entry for localhost:4200.");
        }
    }

    Console.WriteLine();
    Console.WriteLine("✅ WhiteLabel configuration complete!");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
