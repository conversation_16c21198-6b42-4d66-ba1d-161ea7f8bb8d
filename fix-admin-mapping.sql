-- Fix admin user organization mapping
-- This script creates the missing User_Organization_Mapping for the admin user

-- Get the admin user ID
SET @admin_user_id = (SELECT UserId FROM User WHERE Email = '<EMAIL>' LIMIT 1);

-- Check if mapping already exists
SET @mapping_exists = (SELECT COUNT(*) FROM User_Organization_Mapping WHERE UserId = @admin_user_id);

-- Only create mapping if it doesn't exist
INSERT INTO User_Organization_Mapping (UserId, OrganizationId, RoleId, IsApproved, CreatedDate)
SELECT @admin_user_id, 0, 1, 1, NOW()
WHERE @mapping_exists = 0 AND @admin_user_id IS NOT NULL;

-- Verify the setup
SELECT 'Admin User Check:' as Info, UserId, Email, FirstName, LastName FROM User WHERE Email = '<EMAIL>';
SELECT 'Organization Check:' as Info, OrganizationId, Name FROM Organization WHERE OrganizationId = 0;
SELECT 'Role Check:' as Info, RoleId, Name FROM Role WHERE RoleId = 1;
SELECT 'User Mapping Check:' as Info, uom.UserId, uom.OrganizationId, uom.RoleId, uom.IsApproved 
FROM User_Organization_Mapping uom 
INNER JOIN User u ON uom.UserId = u.UserId 
WHERE u.Email = '<EMAIL>';
