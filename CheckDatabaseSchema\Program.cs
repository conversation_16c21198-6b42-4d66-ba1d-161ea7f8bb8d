using System;
using MySqlConnector;

namespace CheckDatabaseSchema
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

            try
            {
                Console.WriteLine("🔍 Checking Database Schema for Configuration Tables...");
                Console.WriteLine();

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");
                    Console.WriteLine();

                    // Check Configuration table schema
                    CheckTableSchema(connection, "Configuration");
                    Console.WriteLine();

                    // Check ConfigurationValues table schema
                    CheckTableSchema(connection, "ConfigurationValues");
                    Console.WriteLine();

                    // Check actual data in Configuration table
                    CheckConfigurationData(connection);
                    Console.WriteLine();

                    // Check actual data in ConfigurationValues table for ConnectWise
                    CheckConnectWiseConfigurationValues(connection);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static void CheckTableSchema(MySqlConnection connection, string tableName)
        {
            Console.WriteLine($"📋 {tableName} Table Schema:");

            var sql = $"DESCRIBE {tableName};";

            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    Console.WriteLine("Field\t\t\tType\t\t\tNull\tKey\tDefault\tExtra");
                    Console.WriteLine("-----\t\t\t----\t\t\t----\t---\t-------\t-----");

                    while (reader.Read())
                    {
                        string field = reader.GetString(0);
                        string type = reader.GetString(1);
                        string nullValue = reader.GetString(2);
                        string key = reader.GetString(3);
                        string defaultValue = reader.IsDBNull(4) ? "NULL" : reader.GetString(4);
                        string extra = reader.GetString(5);

                        Console.WriteLine($"{field}\t\t{type}\t\t{nullValue}\t{key}\t{defaultValue}\t{extra}");
                    }
                }
            }
        }

        static void CheckConfigurationData(MySqlConnection connection)
        {
            Console.WriteLine("📋 Configuration Table Data:");

            var sql = "SELECT * FROM Configuration WHERE Category = 'ConnectWise Api' LIMIT 10;";

            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    if (!reader.HasRows)
                    {
                        Console.WriteLine("❌ No Configuration records found for 'ConnectWise Api'");
                        return;
                    }

                    // Print column headers
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        Console.Write($"{reader.GetName(i)}\t");
                    }
                    Console.WriteLine();

                    // Print data
                    while (reader.Read())
                    {
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var value = reader.IsDBNull(i) ? "NULL" : reader.GetValue(i).ToString();
                            Console.Write($"{value}\t");
                        }
                        Console.WriteLine();
                    }
                }
            }
        }

        static void CheckConnectWiseConfigurationValues(MySqlConnection connection)
        {
            Console.WriteLine("📋 ConnectWise ConfigurationValues Data:");

            var sql = @"
                SELECT
                    cv.ConfigurationValuesId,
                    cv.ConfigurationId,
                    cv.Name,
                    cv.Value,
                    cv.InputType,
                    cv.IsSecret,
                    c.Category
                FROM ConfigurationValues cv
                INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId
                WHERE c.Category = 'ConnectWise Api';";

            using (var cmd = new MySqlCommand(sql, connection))
            {
                using (var reader = cmd.ExecuteReader())
                {
                    if (!reader.HasRows)
                    {
                        Console.WriteLine("❌ No ConfigurationValues found for ConnectWise Api");
                        return;
                    }

                    Console.WriteLine("ValuesId\tConfigId\tName\t\t\tValue\t\t\t\t\tInputType\tIsSecret\tCategory");
                    Console.WriteLine("--------\t--------\t----\t\t\t-----\t\t\t\t\t---------\t--------\t--------");

                    var baseUrlFound = false;
                    while (reader.Read())
                    {
                        int valuesId = reader.GetInt32("ConfigurationValuesId");
                        int configId = reader.GetInt32("ConfigurationId");
                        string name = reader.GetString("Name");
                        string value = reader.IsDBNull(3) ? "NULL" : reader.GetString(3);
                        string inputType = reader.GetString("InputType");
                        bool isSecret = reader.GetBoolean("IsSecret");
                        string category = reader.GetString("Category");

                        if (name == "CWBaseUrl")
                        {
                            baseUrlFound = true;
                            if (string.IsNullOrEmpty(value) || value == "NULL")
                            {
                                Console.WriteLine($"{valuesId}\t\t{configId}\t\t{name}\t\t[NULL/EMPTY] ❌\t\t{inputType}\t\t{isSecret}\t\t{category}");
                            }
                            else
                            {
                                Console.WriteLine($"{valuesId}\t\t{configId}\t\t{name}\t\t{value} ✅\t{inputType}\t\t{isSecret}\t\t{category}");
                            }
                        }
                        else
                        {
                            // Mask secret values
                            string displayValue = isSecret && !string.IsNullOrEmpty(value) && value != "NULL" ? "***HIDDEN***" : value;
                            Console.WriteLine($"{valuesId}\t\t{configId}\t\t{name}\t\t{displayValue}\t\t{inputType}\t\t{isSecret}\t\t{category}");
                        }
                    }

                    if (!baseUrlFound)
                    {
                        Console.WriteLine("❌ CWBaseUrl configuration value not found!");
                        Console.WriteLine("   This is the cause of the ArgumentNullException in ConnectWiseApi.cs line 29");
                    }
                }
            }
        }
    }
}
