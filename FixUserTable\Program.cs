using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Fixing User table schema...");
    Console.WriteLine();

    // Check if is_api_user column exists
    var checkColumnCmd = new MySqlCommand(@"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'myadaptivecloud' 
        AND TABLE_NAME = 'User' 
        AND COLUMN_NAME = 'is_api_user'", connection);
    
    var columnExists = Convert.ToInt32(checkColumnCmd.ExecuteScalar()) > 0;
    
    if (columnExists)
    {
        Console.WriteLine("✓ Column 'is_api_user' already exists in User table.");
    }
    else
    {
        Console.WriteLine("Adding missing 'is_api_user' column to User table...");
        
        // Add the missing column
        var addColumnCmd = new MySqlCommand(@"
            ALTER TABLE User 
            ADD COLUMN is_api_user BIT(1) DEFAULT 0 NOT NULL", connection);
        
        addColumnCmd.ExecuteNonQuery();
        Console.WriteLine("✓ Column 'is_api_user' added successfully!");
    }

    // Also check if IsActive column exists (it might be missing too)
    var checkIsActiveCmd = new MySqlCommand(@"
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'myadaptivecloud' 
        AND TABLE_NAME = 'User' 
        AND COLUMN_NAME = 'IsActive'", connection);
    
    var isActiveExists = Convert.ToInt32(checkIsActiveCmd.ExecuteScalar()) > 0;
    
    if (!isActiveExists)
    {
        Console.WriteLine("Adding missing 'IsActive' column to User table...");
        
        var addIsActiveCmd = new MySqlCommand(@"
            ALTER TABLE User 
            ADD COLUMN IsActive BIT(1) DEFAULT 1 NOT NULL", connection);
        
        addIsActiveCmd.ExecuteNonQuery();
        Console.WriteLine("✓ Column 'IsActive' added successfully!");
    }
    else
    {
        Console.WriteLine("✓ Column 'IsActive' already exists in User table.");
    }

    Console.WriteLine();
    Console.WriteLine("✅ User table schema fix complete!");
    
    // Show the updated table structure
    Console.WriteLine("\nUpdated User table structure:");
    var describeCmd = new MySqlCommand("DESCRIBE User", connection);
    using var reader = describeCmd.ExecuteReader();
    while (reader.Read())
    {
        Console.WriteLine($"  Column: {reader["Field"]}, Type: {reader["Type"]}, Default: {reader["Default"]}");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
