﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Requests.LocalAuthentication;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;

namespace MyAdaptiveCloud.Api.Controllers
{
    [Route("api/[controller]")]
    [Produces("application/json")]
    [ApiController]
    public class LocalAuthenticationController : ControllerBase
    {
        private readonly IUserContextService _userContextService;
        private readonly IPersonService _personService;
        private readonly IIdentityService _identityService;

        public LocalAuthenticationController(IPersonService personService, IUserContextService userContextService, IIdentityService identityService)
            : base()
        {
            _userContextService = userContextService;
            _personService = personService;
            _identityService = identityService;
        }

        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(UnauthorizedResult))]
        [ProducesResponseType(StatusCodes.Status204NoContent, Type = typeof(NoContentResult))]
        [HttpPost("authenticate")]
        public async Task<ActionResult> Authenticate([FromBody] LoginRequest request)
        {
            var user = await _personService.GetPersonByEmail(request.Email, true);

            if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.Password))
            {
                return Unauthorized();
            }

            var userContext = await _userContextService.GetUserContext(user.PersonId, HttpContext.Request.Host.Value);
            if (userContext == null)
            {
                return Unauthorized();
            }

            await _identityService.SignInAndSetAuthenticationCookie(userContext.UserId);
            _identityService.SetUserContextCookie(new Services.Authentication.CurrentUserCookieContent
            {
                OrganizationId = userContext.OrganizationId,
                IsApproved = userContext.IsApproved
            });

            return NoContent();
        }
    }
}