import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';

import { AuthService } from '@app/shared/services/auth.service';
import { UserContextService } from '../services/user-context.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
    private readonly userService = inject(UserContextService);
    private readonly authService = inject(AuthService);
    private readonly router = inject(Router);

    canActivate(): boolean {
        const currentUser = this.userService.currentUser;
        if (!currentUser) {
            // Check if we're trying to access local authentication routes
            const currentUrl = this.router.url;
            if (currentUrl.startsWith('/local-authentication')) {
                // Allow access to local authentication routes without requiring existing authentication
                return true;
            }

            this.authService.redirectToSSOLogin(true);
            return false;
        }
        return true;
    }
}
