import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';
import { LoginRequest } from '../../requests/login.request';
import { LocalAuthenticationService } from '../../services/local-authentication.service';
import { LoginForm } from './login.form';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
    selector: 'app-login',
    imports: [ReactiveFormsModule],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginComponent {
    private readonly fb = inject(FormBuilder);
    private readonly userContextService = inject(UserContextService);
    private readonly authenticationService = inject(LocalAuthenticationService);
    private readonly router = inject(Router);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly form: FormGroup<LoginForm> = this.fb.group<LoginForm>({
        email: this.fb.control(null, [Validators.required, Validators.email]),
        password: this.fb.control(null, Validators.required)
    });

    protected readonly errorMessage = signal<string | null>(null);
    protected readonly isLoading = signal<boolean>(false);

    protected submitForm(): void {
        if (this.form.valid) {
            this.errorMessage.set(null);
            this.isLoading.set(true);

            const request = this.form.getRawValue() as LoginRequest;
            this.authenticationService.login(request)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                    next: () => {
                        this.userContextService.initializeUserContext().then(() => {
                            this.isLoading.set(false);
                            if (this.userContextService.currentUser) {
                                this.router.navigate(['/home']);
                            }
                        });
                    },
                    error: (error: HttpErrorResponse) => {
                        this.isLoading.set(false);
                        if (error.status === 401) {
                            this.errorMessage.set('Invalid email or password. Please try again.');
                        } else {
                            this.errorMessage.set('An error occurred during login. Please try again.');
                        }
                    }
                });
        }
    }

}
