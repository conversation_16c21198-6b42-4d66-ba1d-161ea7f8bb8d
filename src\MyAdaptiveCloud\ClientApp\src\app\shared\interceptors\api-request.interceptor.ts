import { HttpErrorResponse, HttpEvent, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { EMPTY, Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NotificationType } from '../models/notification-type';
import { AuthService } from '../services/auth.service';
import { LoadingService } from '../services/loading.service';
import { NotificationService } from '../services/notification.service';
import { UserContextService } from '../services/user-context.service';

@Injectable()
export class ApiRequestInterceptor implements HttpInterceptor {
    private readonly authService = inject(AuthService);
    private readonly loadingService = inject(LoadingService);
    private readonly cookieService = inject(CookieService);
    private readonly notificationService = inject(NotificationService);

    private default403Message = 'You do not have permission to complete the required action';

    private readonly userContextService = inject(UserContextService);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        if (request.url?.includes(this.userContextService.currentUser?.cloudInfraUserContext?.apiUrl)) {
            return next.handle(request);
        }

        request = request.clone({
            headers: request.headers.set('X-IPP-UI-Client-TimeZone', Intl.DateTimeFormat().resolvedOptions().timeZone)
        });
        return next.handle(request).pipe(catchError(err => this.handleError(err, this.loadingService, this.authService, this.cookieService)));
    }

    public handleError(
        error: HttpErrorResponse,
        loadingService: LoadingService,
        authService: AuthService,
        cookieService: CookieService
    ): Observable<never> {
        loadingService.endAllRequests();
        if (error instanceof HttpErrorResponse) {
            switch (error.status) {
                case 400: {
                    return throwError(() => error);
                }
                case 401: {
                    // Don't redirect to SSO for local authentication endpoints
                    // Allow the local authentication form to handle 401 errors properly
                    if (error.url && error.url.includes('/api/localauthentication/')) {
                        return throwError(() => error);
                    }

                    cookieService.delete('CurrentUser');
                    authService.redirectToSSOLoginWithPrompt(true);
                    break;
                }
                case 403: {
                    this.notificationService.notify(this.default403Message, NotificationType.Error);
                    break;
                }
                case 404: {
                    return throwError(() => error);
                }
                default: {
                    return EMPTY;
                }
            }
        }
        return EMPTY;
    }

}
