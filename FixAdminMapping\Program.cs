﻿using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("Connected to MySQL database successfully.");

    // Check if ***** user exists
    var checkUserCmd = new MySqlCommand("SELECT UserId FROM User WHERE Email = '<EMAIL>'", connection);
    var *****UserId = checkUserCmd.ExecuteScalar();

    if (*****UserId == null)
    {
        Console.WriteLine("Admin user not found!");
        return;
    }

    Console.WriteLine($"Found ***** user with ID: {*****UserId}");

    // First, let's check the table structure
    try
    {
        var describeCmd = new MySqlCommand("DESCRIBE User_Organization_Mapping", connection);
        using var descReader = describeCmd.ExecuteReader();
        Console.WriteLine("User_Organization_Mapping table structure:");
        while (descReader.Read())
        {
            Console.WriteLine($"  {descReader["Field"]}: {descReader["Type"]}");
        }
        descReader.Close();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error describing table: {ex.Message}");
    }

    // Check if organization mapping exists (using UserOrganizationId which seems to be the actual column)
    var checkMappingCmd = new MySqlCommand($"SELECT COUNT(*) FROM User_Organization_Mapping WHERE UserOrganizationId = {*****UserId}", connection);
    var mappingCount = Convert.ToInt32(checkMappingCmd.ExecuteScalar());

    if (mappingCount > 0)
    {
        Console.WriteLine("Admin user already has organization mapping.");
    }
    else
    {
        Console.WriteLine("Creating organization mapping for ***** user...");

        // Create the mapping (using the actual column names from the table structure)
        var createMappingCmd = new MySqlCommand($@"
            INSERT INTO User_Organization_Mapping (UserOrganizationId, RoleId, IsApproved, CreatedDate)
            VALUES ({*****UserId}, 1, 1, NOW())", connection);

        var rowsAffected = createMappingCmd.ExecuteNonQuery();
        Console.WriteLine($"Organization mapping created. Rows affected: {rowsAffected}");
    }

    // Also ensure there's a proper white label entry for localhost
    Console.WriteLine("Checking white label configuration for localhost...");
    var checkWhiteLabelCmd = new MySqlCommand("SELECT COUNT(*) FROM WhiteLabel WHERE DomainName = 'localhost' OR AdaptiveCloudHostname = 'localhost'", connection);
    var whiteLabelCount = Convert.ToInt32(checkWhiteLabelCmd.ExecuteScalar());

    if (whiteLabelCount == 0)
    {
        Console.WriteLine("Creating white label entry for localhost...");
        var createWhiteLabelCmd = new MySqlCommand(@"
            INSERT INTO WhiteLabel (OrganizationId, PortalName, DomainName, PrimaryColor, SecondaryColor, CreatedDate, IsActive, AdaptiveCloudHostname)
            VALUES (0, 'MyAdaptiveCloud Dev', 'localhost', '#3079A7', '#666666', NOW(), 1, 'localhost')", connection);

        var whiteLabelRows = createWhiteLabelCmd.ExecuteNonQuery();
        Console.WriteLine($"White label entry created. Rows affected: {whiteLabelRows}");
    }
    else
    {
        Console.WriteLine("White label entry for localhost already exists.");
    }

    // Verify the setup (using correct column names)
    var verifyCmd = new MySqlCommand($@"
        SELECT u.Email, u.FirstName, u.LastName, uom.UserOrganizationId, uom.RoleId, uom.IsApproved
        FROM User u
        INNER JOIN User_Organization_Mapping uom ON u.UserId = uom.UserOrganizationId
        WHERE u.Email = '<EMAIL>'", connection);

    using var reader = verifyCmd.ExecuteReader();
    if (reader.Read())
    {
        Console.WriteLine("\nAdmin user verification:");
        Console.WriteLine($"Email: {reader["Email"]}");
        Console.WriteLine($"Name: {reader["FirstName"]} {reader["LastName"]}");
        Console.WriteLine($"User Organization ID: {reader["UserOrganizationId"]}");
        Console.WriteLine($"Role ID: {reader["RoleId"]}");
        Console.WriteLine($"Is Approved: {reader["IsApproved"]}");
        Console.WriteLine("\nAdmin user setup is complete!");
    }
    else
    {
        Console.WriteLine("Could not verify ***** user setup.");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
}

Console.WriteLine("Press any key to exit...");
Console.ReadKey();
