using System;
using System.IO;
using MySqlConnector;

class FixAdminMapping
{
    static void Main(string[] args)
    {
        string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";
        
        try
        {
            using (var connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Connected to MySQL database successfully.");
                
                // Read the SQL script
                string sqlScript = File.ReadAllText("fix-*****-mapping.sql");
                
                // Split the script into individual statements
                var statements = sqlScript.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                
                foreach (var statement in statements)
                {
                    var trimmedStatement = statement.Trim();
                    if (string.IsNullOrWhiteSpace(trimmedStatement) || trimmedStatement.StartsWith("--"))
                        continue;
                    
                    try
                    {
                        var command = new MySqlCommand(trimmedStatement, connection);
                        var result = command.ExecuteScalar();
                        
                        // If it's a SELECT statement, print the result
                        if (trimmedStatement.ToUpper().Contains("SELECT"))
                        {
                            Console.WriteLine($"Query result: {result}");
                        }
                        else
                        {
                            var rowsAffected = command.ExecuteNonQuery();
                            Console.WriteLine($"Statement executed. Rows affected: {rowsAffected}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error executing statement: {trimmedStatement.Substring(0, Math.Min(50, trimmedStatement.Length))}...");
                        Console.WriteLine($"Error: {ex.Message}");
                    }
                }
                
                Console.WriteLine("Admin mapping fix completed.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Database connection error: {ex.Message}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
