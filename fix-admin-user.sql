-- Fix admin user authentication by ensuring proper organization mapping
-- This script ensures the admin user can authenticate by creating necessary mappings

-- 1. Ensure Root organization exists
INSERT IGNORE INTO `Organization` (`OrganizationId`, `Name`, `CreatedDate`, `IsActive`, `AllowSubOrg`, `AllowWhiteLabel`, `IsPartner`, `IsVerified`, `ParentOrganizationId`) 
VALUES (0, 'Root', NOW(), 1, 1, 1, 0, 1, null);

-- 2. Ensure Super Admin role exists
INSERT IGNORE INTO `Role` (`RoleId`, `Name`, `OrganizationId`, `Permissions`, `AvailableTo`, `IsRestricted`, `Description`) 
VALUES (1, 'Super Admin', 0, 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF, 1, 1, 'Super Admin');

-- 3. Ensure admin user exists
INSERT IGNORE INTO `User` (`FirstName`, `Email`, `LastName`, `Password`, `CreatedDate`) 
VALUES ('Local Admin', '<EMAIL>', 'Local Admin', '$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y', now());

-- 4. Get the admin user ID
SET @admin_user_id = (SELECT UserId FROM User WHERE Email = '<EMAIL>' LIMIT 1);

-- 5. Create User-Organization mapping for admin user (this is likely what's missing!)
INSERT IGNORE INTO `User_Organization_Mapping` (`UserId`, `OrganizationId`, `RoleId`, `IsApproved`, `CreatedDate`) 
VALUES (@admin_user_id, 0, 1, 1, NOW());

-- 6. Ensure default white label exists
INSERT IGNORE INTO `WhiteLabel` (`WhiteLabelId`, `OrganizationId`, `PortalName`, `DomainName`, `PrimaryColor`, `SecondaryColor`, `CreatedDate`, `IsActive`, `AdaptiveCloudHostname`) 
VALUES (1, 0, 'MyAdaptiveCloud', 'localhost', '#3079A7', '#666666', now(), 1, 'localhost');

-- Verify the setup
SELECT 'Admin User:' as Info, UserId, Email, FirstName, LastName FROM User WHERE Email = '<EMAIL>';
SELECT 'Organization:' as Info, OrganizationId, Name FROM Organization WHERE OrganizationId = 0;
SELECT 'Role:' as Info, RoleId, Name FROM Role WHERE RoleId = 1;
SELECT 'User Mapping:' as Info, uom.UserId, uom.OrganizationId, uom.RoleId, uom.IsApproved 
FROM User_Organization_Mapping uom 
INNER JOIN User u ON uom.UserId = u.UserId 
WHERE u.Email = '<EMAIL>';
