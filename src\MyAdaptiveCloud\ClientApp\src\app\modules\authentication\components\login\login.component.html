<div class="wrapper">
    <!-- START card-->
    <div class="card card-flat">
        <div class="card-body">
            <p class="text-center py-2">SIGN IN TO CONTINUE</p>
            @if (errorMessage()) {
            <div class="alert alert-danger" role="alert">
                {{ errorMessage() }}
            </div>
            }
            <form [formGroup]="form" class="form-validate mb-3" novalidate>
                <div class="mb-3">
                    <div class="input-group with-focus">
                        <input class="form-control border-right-0" type="email" placeholder="Enter email"
                            formControlName="email" autocomplete="username" />
                        <div>
                            <span class="input-group-text h-100 text-muted bg-transparent border-left-0">
                                <em class="fa fa-envelope"></em>
                            </span>
                        </div>
                    </div>
                    @if (form.controls.email.hasError('required') && (form.controls.email.dirty ||
                    form.controls.email.touched)) {
                    <div class="text-danger">
                        This field is required</div>
                    }
                    @if (form.controls.email.hasError('email') && (form.controls.email.dirty ||
                    form.controls.email.touched)) {
                    <div class="text-danger">
                        This field must be a valid email address</div>
                    }
                </div>
                <div class="mb-3">
                    <div class="input-group with-focus">
                        <input class="form-control border-right-0" type="password" placeholder="Password"
                            autocomplete="current-password" formControlName="password" />
                        <div>
                            <span class="input-group-text h-100 text-muted bg-transparent border-left-0">
                                <em class="fa fa-lock"></em>
                            </span>
                        </div>
                    </div>
                    @if (form.controls.password.hasError('required') && (form.controls.password.dirty ||
                    form.controls.password.touched)) {
                    <div class="text-danger">
                        This field is required</div>
                    }
                </div>
                <div class="d-grid">
                    <button class="btn btn-primary" type="submit" [disabled]="form.invalid || isLoading()"
                        (click)="submitForm()">
                        @if (isLoading()) {
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing in...
                        } @else {
                        Login
                        }
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>