using System;
using System.IO;
using MySqlConnector;

namespace AddKeyCloakConfig
{
    class Program
    {
        static void Main(string[] args)
        {
            string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

            try
            {
                Console.WriteLine("🔧 Adding KeyCloak configuration to fix 401 authentication error...");
                Console.WriteLine();

                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✅ Connected to myadaptivecloud database successfully.");

                    // Read and execute the SQL script
                    string sqlScript = File.ReadAllText("add-keycloak-config.sql");

                    // Split the script into individual commands
                    string[] commands = sqlScript.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (string command in commands)
                    {
                        string trimmedCommand = command.Trim();
                        if (!string.IsNullOrEmpty(trimmedCommand) && !trimmedCommand.StartsWith("--"))
                        {
                            using (var cmd = new MySqlCommand(trimmedCommand, connection))
                            {
                                var result = cmd.ExecuteScalar();

                                // If this is a SELECT command, display results
                                if (trimmedCommand.ToUpper().Contains("SELECT"))
                                {
                                    using (var selectCmd = new MySqlCommand(trimmedCommand, connection))
                                    {
                                        using (var reader = selectCmd.ExecuteReader())
                                        {
                                            if (reader.HasRows)
                                            {
                                                Console.WriteLine("\n📋 KeyCloak Configuration Status:");
                                                Console.WriteLine("Category\t\tName\t\t\tValue\t\t\t\tIsSecret");
                                                Console.WriteLine("--------\t\t----\t\t\t-----\t\t\t\t--------");

                                                while (reader.Read())
                                                {
                                                    if (reader.FieldCount >= 4)
                                                    {
                                                        string category = reader.IsDBNull(1) ? "" : reader.GetString(1);
                                                        string name = reader.IsDBNull(2) ? "" : reader.GetString(2);
                                                        string value = reader.IsDBNull(3) ? "" : reader.GetString(3);
                                                        bool isSecret = reader.IsDBNull(4) ? false : reader.GetBoolean(4);

                                                        Console.WriteLine($"{category}\t\t{name}\t\t{value}\t\t{isSecret}");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    break; // Only process the first SELECT
                                }
                            }
                        }
                    }

                    Console.WriteLine("\n✅ KeyCloak configuration added successfully!");
                    Console.WriteLine("\n🎯 Next Steps:");
                    Console.WriteLine("1. Restart your backend application");
                    Console.WriteLine("2. Test the authentication endpoint:");
                    Console.WriteLine("   curl -k -v https://localhost:5001/api/authentication/LoginOpenIdConnectWithPrompt?path=http://localhost:4200/");
                    Console.WriteLine("3. You should now see a redirect to KeyCloak SSO instead of a 401 error");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
