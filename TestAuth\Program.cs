using MySqlConnector;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("=== Authentication Debug Test ===");

    // 1. Check if ***** user exists and get details
    var userCmd = new MySqlCommand("SELECT UserId, Email, FirstName, LastName, Password FROM User WHERE Email = '<EMAIL>'", connection);
    using var userReader = userCmd.ExecuteReader();
    if (userReader.Read())
    {
        Console.WriteLine($"✓ User found: {userReader["Email"]} (ID: {userReader["UserId"]})");
        Console.WriteLine($"  Name: {userReader["FirstName"]} {userReader["LastName"]}");
        Console.WriteLine($"  Password hash: {userReader["Password"]}");
        var userId = userReader["UserId"];
        userReader.Close();

        // 2. Check organization mapping
        var mappingCmd = new MySqlCommand($"SELECT * FROM User_Organization_Mapping WHERE UserOrganizationId = {userId}", connection);
        using var mappingReader = mappingCmd.ExecuteReader();
        if (mappingReader.Read())
        {
            Console.WriteLine($"✓ Organization mapping found:");
            Console.WriteLine($"  Mapping ID: {mappingReader["MappingId"]}");
            Console.WriteLine($"  Role ID: {mappingReader["RoleId"]}");
            Console.WriteLine($"  Is Approved: {mappingReader["IsApproved"]}");
            Console.WriteLine($"  Created Date: {mappingReader["CreatedDate"]}");
            mappingReader.Close();
        }
        else
        {
            Console.WriteLine("✗ No organization mapping found!");
            mappingReader.Close();
        }

        // 3. Check organizations
        var orgCmd = new MySqlCommand("SELECT * FROM Organization WHERE OrganizationId = 0", connection);
        using var orgReader = orgCmd.ExecuteReader();
        if (orgReader.Read())
        {
            Console.WriteLine($"✓ Root organization found: {orgReader["Name"]} (ID: {orgReader["OrganizationId"]})");
            orgReader.Close();
        }
        else
        {
            Console.WriteLine("✗ Root organization not found!");
            orgReader.Close();
        }

        // 4. Check white labels
        var whiteLabelCmd = new MySqlCommand("SELECT * FROM WhiteLabel", connection);
        using var wlReader = whiteLabelCmd.ExecuteReader();
        Console.WriteLine("White Label entries:");
        while (wlReader.Read())
        {
            Console.WriteLine($"  ID: {wlReader["WhiteLabelId"]}, Org: {wlReader["OrganizationId"]}, Domain: {wlReader["DomainName"]}, Hostname: {wlReader["AdaptiveCloudHostname"]}");
        }
        wlReader.Close();

        // 5. Check roles
        var roleCmd = new MySqlCommand("SELECT * FROM Role WHERE RoleId = 1", connection);
        using var roleReader = roleCmd.ExecuteReader();
        if (roleReader.Read())
        {
            Console.WriteLine($"✓ Super Admin role found: {roleReader["Name"]} (ID: {roleReader["RoleId"]})");
            roleReader.Close();
        }
        else
        {
            Console.WriteLine("✗ Super Admin role not found!");
            roleReader.Close();
        }

        // 6. Test password verification
        string testPassword = "*****";
        string storedHash = "$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y";
        Console.WriteLine($"Password test: '{testPassword}' against stored hash");
        Console.WriteLine($"Expected hash: {storedHash}");

        // Test BCrypt verification
        bool isPasswordValid = BCrypt.Net.BCrypt.Verify(testPassword, storedHash);
        Console.WriteLine($"BCrypt verification result: {isPasswordValid}");

        if (!isPasswordValid)
        {
            Console.WriteLine("✗ Password verification failed!");
            // Try generating a new hash to see what it should be
            string newHash = BCrypt.Net.BCrypt.HashPassword(testPassword);
            Console.WriteLine($"New hash for '{testPassword}': {newHash}");
        }
        else
        {
            Console.WriteLine("✓ Password verification successful!");
        }

    }
    else
    {
        Console.WriteLine("✗ Admin user not found!");
        userReader.Close();
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
