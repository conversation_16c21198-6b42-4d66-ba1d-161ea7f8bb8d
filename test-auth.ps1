# Test script to verify authentication issue
Write-Host "Testing MyAdaptiveCloud Authentication..." -ForegroundColor Blue

# Test 1: Check if backend is running
Write-Host "`n1. Checking if backend is running on port 5001..." -ForegroundColor Yellow
$backendRunning = Test-NetConnection -ComputerName localhost -Port 5001 -InformationLevel Quiet
if ($backendRunning) {
    Write-Host "✓ Backend is running on port 5001" -ForegroundColor Green
} else {
    Write-Host "✗ Backend is NOT running on port 5001" -ForegroundColor Red
    exit 1
}

# Test 2: Check if MySQL is running
Write-Host "`n2. Checking if MySQL is running on port 3306..." -ForegroundColor Yellow
$mysqlRunning = Test-NetConnection -ComputerName localhost -Port 3306 -InformationLevel Quiet
if ($mysqlRunning) {
    Write-Host "✓ MySQL is running on port 3306" -ForegroundColor Green
} else {
    Write-Host "✗ MySQL is NOT running on port 3306" -ForegroundColor Red
}

# Test 3: Test authentication endpoint
Write-Host "`n3. Testing authentication endpoint..." -ForegroundColor Yellow

# Disable SSL certificate validation for testing
[System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}

try {
    $body = @{
        email = "<EMAIL>"
        password = "admin"
    } | ConvertTo-Json

    $headers = @{
        "Content-Type" = "application/json"
    }

    Write-Host "Sending POST request to: https://localhost:5001/api/localauthentication/authenticate"
    Write-Host "Body: $body"
    
    $response = Invoke-WebRequest -Uri "https://localhost:5001/api/localauthentication/authenticate" -Method POST -Headers $headers -Body $body -UseBasicParsing
    
    Write-Host "✓ Authentication successful!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Authentication failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
        
        # Try to read the response content
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
}

# Test 4: Test with different credentials
Write-Host "`n4. Testing with different credentials..." -ForegroundColor Yellow

$testCredentials = @(
    @{ email = "<EMAIL>"; password = "password" },
    @{ email = "<EMAIL>"; password = "Admin123" },
    @{ email = "admin@localhost"; password = "admin" }
)

foreach ($cred in $testCredentials) {
    try {
        $body = $cred | ConvertTo-Json
        Write-Host "Testing: $($cred.email) / $($cred.password)"
        
        $response = Invoke-WebRequest -Uri "https://localhost:5001/api/localauthentication/authenticate" -Method POST -Headers $headers -Body $body -UseBasicParsing
        Write-Host "✓ Success with $($cred.email)" -ForegroundColor Green
        break
    } catch {
        Write-Host "✗ Failed with $($cred.email): $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    }
}

Write-Host "`nTest completed." -ForegroundColor Blue
