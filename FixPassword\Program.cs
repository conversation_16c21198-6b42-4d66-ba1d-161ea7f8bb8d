using MySqlConnector;
using BCrypt.Net;

string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;Database=myadaptivecloud;";

try
{
    using var connection = new MySqlConnection(connectionString);
    connection.Open();
    Console.WriteLine("🔧 Fixing ***** user password hash...");
    Console.WriteLine();

    // Generate correct hash for "*****" password
    string correctPassword = "*****";
    string correctHash = BCrypt.Net.BCrypt.HashPassword(correctPassword);

    Console.WriteLine($"Generating new hash for password: '{correctPassword}'");
    Console.WriteLine($"New hash: {correctHash}");
    Console.WriteLine();

    // Verify the new hash works
    bool verification = BCrypt.Net.BCrypt.Verify(correctPassword, correctHash);
    Console.WriteLine($"Hash verification test: {verification}");

    if (!verification)
    {
        Console.WriteLine("❌ Hash generation failed!");
        return;
    }

    Console.WriteLine("✅ Hash generation successful!");
    Console.WriteLine();

    // Update the ***** user's password hash
    var updateCmd = new MySqlCommand(@"
        UPDATE User 
        SET Password = @newHash 
        WHERE Email = '<EMAIL>'", connection);

    updateCmd.Parameters.AddWithValue("@newHash", correctHash);

    int rowsAffected = updateCmd.ExecuteNonQuery();

    if (rowsAffected > 0)
    {
        Console.WriteLine($"✅ Updated password <NAME_EMAIL> ({rowsAffected} row(s) affected)");

        // Verify the update worked by reading it back
        var verifyCmd = new MySqlCommand("SELECT Password FROM User WHERE Email = '<EMAIL>'", connection);
        var storedHash = verifyCmd.ExecuteScalar()?.ToString();

        if (storedHash != null)
        {
            bool finalVerification = BCrypt.Net.BCrypt.Verify(correctPassword, storedHash);
            Console.WriteLine($"Final verification test: {finalVerification}");

            if (finalVerification)
            {
                Console.WriteLine("🎉 Password fix complete! Admin user can now login with password '*****'");
            }
            else
            {
                Console.WriteLine("❌ Final verification failed!");
            }
        }
    }
    else
    {
        Console.WriteLine("❌ No rows were updated. Admin user might not exist.");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
