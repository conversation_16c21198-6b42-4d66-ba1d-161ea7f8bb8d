using BCrypt.Net;

string testPassword = "admin";
string storedHash = "$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y";

Console.WriteLine("🔐 Testing BCrypt password verification...");
Console.WriteLine($"Password: '{testPassword}'");
Console.WriteLine($"Stored hash: {storedHash}");
Console.WriteLine();

// Test BCrypt verification
bool isValid = BCrypt.Net.BCrypt.Verify(testPassword, storedHash);
Console.WriteLine($"BCrypt.Verify result: {isValid}");

if (!isValid)
{
    Console.WriteLine("❌ Password verification failed!");
    Console.WriteLine();

    // Generate a new hash for comparison
    string newHash = BCrypt.Net.BCrypt.HashPassword(testPassword);
    Console.WriteLine($"New hash for '{testPassword}': {newHash}");

    // Test the new hash
    bool newHashValid = BCrypt.Net.BCrypt.Verify(testPassword, newHash);
    Console.WriteLine($"New hash verification: {newHashValid}");

    Console.WriteLine();
    Console.WriteLine("🔧 Possible solutions:");
    Console.WriteLine("1. Update the database with the correct password hash");
    Console.WriteLine("2. Check if the original password is different from 'admin'");
}
else
{
    Console.WriteLine("✅ Password verification successful!");
}

Console.WriteLine("\nPress any key to exit...");
Console.ReadKey();
